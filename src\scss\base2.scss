@import "variables";

html,
body {
  min-height: 100%;
  height: 100%;
  min-width: 1000px;
  background: $backColor;
}
a {
  color: $blueTheme;
}
a:hover {
  color: $blueTheme;
  text-decoration: none;
}
h1,
h2,
h3,
h4,
h5,
h6,
p {
  padding: 0px;
  margin: 0;
  line-height: 200%;
}
em,
i {
  font-style: normal;
}
.cr-pd-mr {
  padding: 0px;
  margin: 0px;
}
.cl-red {
  color: red !important;
}
.cl-orange {
  color: #ff5a2c !important;
}
.ft-cen {
  text-align: center;
}
.more {
  color: $blueTheme;
}
.cl-basics {
  color: $master-color-5;
}
.hv-poin:hover {
  cursor: pointer;
}
.pd-left {
  padding-left: 15px;
}
.pd-rit {
  padding-right: 15px;
}
.btn-primary {
  background: $master-color-5;
  border: none;
}
.cl-boder {
  border: none;
}
.hide {
  display: none;
}
.show {
  display: block;
}
