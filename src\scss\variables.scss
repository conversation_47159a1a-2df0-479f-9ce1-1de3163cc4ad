@charset "UTF-8"; 
/*
 * colors variables
 */
// 3色 4色 配色方案
$orange:#f07700;
$blue:#75aaef;
$lg-blue:#e9f7ff;
$purple:#877cd4;
$deep-purple:#5f1898;
$powder:#d77ba5;
$green:#7cd877;
$gray:#dadada;
$light-blue:#0077cc;
$font-red:#dd340d;

$black:#000;
$backColor: #f3f5f7;
$font-color:#050505;
$lightGrey:#14191e;
$master-color-0: #fff;// 白色：主体背景、导航链接、、
$master-color-1: #333;// 黑色：说明类字体颜色、、、、
$master-color-2: #999;//  灰色：目的地、链接字体颜色、、
$master-color-3: #ff7c2d;// 橙色：主题组件颜色、装饰类、反转字体、、
$master-color-4:#787d82;// 米白色：头部、底部背景颜色、、、
$master-color-5:#00a4ff;//网站基础色
$master-color-6:#f4f4f4;//网站背景色
$master-color-7:#ccc;
$master-color-8:#bdbdbd;//课程背景色
$master-color-9:rgba(255,255,203,0.8);//这个是习题答案页的头颜色
$master-color-10:#14191f;//video 配色

$navbarColor: #363c40;// 深灰色：头部导航背景色、、、、


$blueTheme:#2da1e7;  //蓝色：推荐区1颜色
$greenTheme:#50b400;  //绿色：推荐区2颜色、商品列表边框、、、
$borderColor:#ddd;   //灰色：边框颜色


