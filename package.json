{"name": "project-xczx2-portal-vue-ts", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "serve-prod": "vue-cli-service serve --mode prod", "build": "vue-cli-service build", "build-prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.19.0", "core-js": "^3.3.2", "crypto-js": "^4.1.1", "element-ui": "2.11.1", "js-base64": "^2.5.1", "js-cookie": "^2.2.1", "moment": "^2.24.0", "nprogress": "^0.2.0", "qiniu-js": "^2.5.5", "sha.js": "^2.4.11", "vue": "^2.6.10", "vue-class-component": "^7.0.2", "vue-property-decorator": "^8.3.0", "vue-router": "^3.1.3", "vuex": "^3.0.1", "vuex-class": "^0.3.2", "vuex-module-decorators": "^0.11.0"}, "devDependencies": {"@types/js-cookie": "^2.2.4", "@types/nprogress": "^0.2.0", "@vue/cli-plugin-babel": "^4.0.0", "@vue/cli-plugin-eslint": "^4.0.0", "@vue/cli-plugin-router": "^4.0.0", "@vue/cli-plugin-typescript": "^4.0.0", "@vue/cli-plugin-vuex": "^4.0.0", "@vue/cli-service": "^4.0.0", "@vue/eslint-config-typescript": "^4.0.0", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "sass": "^1.19.0", "sass-loader": "^8.0.0", "typescript": "~3.5.3", "vue-template-compiler": "^2.6.10"}}