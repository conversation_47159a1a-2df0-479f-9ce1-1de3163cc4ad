<template>
  <div class="step-body">
    <div class="agreement-wrapper">
      <!-- TODO: 等待需求提供入驻协议 -->
      <el-scrollbar style="height:100%">
        <div>入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议入驻协议</div>
      </el-scrollbar>
    </div>
    <div class="read-and-agreement">
      <el-checkbox v-model="syncedSignAgreement">我已阅读并同意此协议</el-checkbox>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, PropSync } from 'vue-property-decorator'

@Component
export default class CommonEnteringStep1SignAgreement extends Vue {
  @PropSync('signAgreement', { type: Boolean, required: true, default: false })
  syncedSignAgreement!: boolean
}
</script>

<style lang="scss" scoped>
.step-body {
  .agreement-wrapper {
    padding: 10px;
    border: 1px solid #ededed;
    height: 400px;
  }

  .read-and-agreement {
    padding: 7px 10px 3px;
    border: 1px solid #ededed;
    border-top: none;
  }
}
</style>