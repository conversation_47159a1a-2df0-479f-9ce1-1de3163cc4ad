server {
    listen 80;

    root /usr/share/nginx/html;

    location / {
        try_files $uri $uri/ /index.html;
    }
    location ^~ /api/ {
        rewrite ^/api/(.*)$ /mock/713/$1 break;
        proxy_pass https://mock.boxuegu.com;
        # proxy_set_header   X-Forwarded-Proto $scheme;
        # proxy_set_header   Host              $http_host;
        # proxy_set_header   X-Real-IP         $remote_addr;
    }
}
