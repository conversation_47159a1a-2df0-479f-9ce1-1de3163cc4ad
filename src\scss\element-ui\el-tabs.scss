// Tabs 标签页

// .el-tabs {
//   // 去阴影
//   .el-tabs--border-card {
//     border: 0px solid #dcdfe6;
//     -webkit-box-shadow: 0 0px 0px 0;
//     box-shadow: 0 0px 0px 0;
//   }
//   // 背景
//   .el-tabs__nav-scroll {
//     background-color: #2cb4ff;
//     padding-left: 60px;
//     padding-top: 6px;
//   }
//   // 项
//   .el-tabs__item.is-top.is-active {
//     color: #333333;
//   }
//   .el-tabs__item.is-top {
//     color: #ffffff;
//     border-radius: 6px 6px 0px 0px;
//   }
//   .el-tabs__item.is-top:hover {
//     color: #999999;
//   }
// }
