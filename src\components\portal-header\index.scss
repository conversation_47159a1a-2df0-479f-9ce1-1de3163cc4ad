@import "@/scss/variables";

header {
  width: 100%;
  background: $master-color-0;

  a {
    color: $master-color-1;

    &:hover {
      color: $master-color-3;
      text-decoration: none;
    }
  }

  .navbar {
    min-width: 1024px;
    margin: 0 auto;
    border-radius: 0px;
    /* overflow: hidden; */

    .logo {
      width: 195px;
      float: left;
      min-width: 110px;
      margin-right: 2%;
    }

    .starch {
      float: right;
      min-width: 326px;
      margin-right: 5%;
    }

    .sign-in {
      float: right;

      a {
        color: $master-color-10;
      }

      span {
        color: $master-color-4;
      }

      .personal {
        display: inline-block;
        margin-right: 10px;
        color: $master-color-4;

        .personalIco::before {
          content: " ● ";
          color: red;
          font-weight: bold;
          right: 1px;
          top: -8px;
          position: absolute;
        }

        span {
          display: inline-block;
          width: 20px;
          height: 20px;
          position: relative;
          top: 6px;
          margin-left: 8px;
          background: url(../../assets/img/personalIco.png) no-repeat;
        }
      }

      .myInfo {
        color: $master-color-4;

        img {
          display: inline-block;
          margin: 0 5px;
          position: relative;
          top: -2px;
          width: 25px;
          height: 25px;
          border-radius: 25px;
        }
      }
    }
  }

  .navbar-nav {
    font-size: 18px;
    margin: 0 20px;
    font-weight: bold;
    color: $master-color-1;

    li a {
      padding: 9px 15px !important;
    }
  }

  .active {
    border-bottom: solid 2px $master-color-5;
  }

  .sign-in {
    margin-top: 12px;
    margin-right: 0px;
  }

  .sign-in a {
    color: $master-color-5;
  }

  .learingHeader {
    min-width: 1366px;
    max-width: 1366px;
    margin: 0 auto;
    background: $master-color-0;
    width: 100%;
    padding-top: 30px;
    padding-bottom: 30px;
  }

  input {
    height: 35px;
    margin-top: 7px;
  }

  .input-search {
    border: solid 2px $master-color-5;
    width: 280px;
    padding-left: 10px;
  }

  .search-buttom {
    border: solid 2px $master-color-5;
    background: $master-color-5;
    color: $master-color-0;
    font-weight: bold;
    width: 14%;
    max-width: 80px;
    border-radius: 0;
    position: relative;
  }

  /* 下拉按钮样式 */
  .dropbtn {
    /* background-color: #4CAF50; */
    color: white;
    /* padding: 16px;
    font-size: 16px; */
    border: none;
    cursor: pointer;
  }

  /* 容器 <div> - 需要定位下拉内容 */
  .dropdown {
    position: relative;
    display: inline-block;
  }

  /* 下拉内容 (默认隐藏) */
  .dropdown-content {
    display: none;
    position: absolute;
    z-index: 9999;
    background-color: #f9f9f9;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  }

  /* 下拉菜单的链接 */
  .dropdown-content a {
    /* color: black; */
    color: $master-color-4 !important;
    padding: 12px 16px;
    text-decoration: none;
    display: block;
  }

  /* 鼠标移上去后修改下拉菜单链接颜色 */
  .dropdown-content a:hover {
    background-color: #f1f1f1
  }

  /* 在鼠标移上去后显示下拉菜单 */
  .dropdown:hover .dropdown-content {
    display: block;
  }

  /* 当下拉内容显示后修改下拉按钮的背景颜色 */
  /*
  .dropdown:hover .dropbtn {
    background-color: #3e8e41;
  }
  */
}