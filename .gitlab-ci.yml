stages:
  - sonar
  - build

# sonar_preview:
#   stage: sonar
#   script:
#     - ci/sonar_preview.sh
#   tags:
#     - centos-aliyun-158
#   only:
#     - master

sonar_analyze:
  stage: sonar
  script:
    - |
        sonar-scanner -X \
        -Dsonar.projectKey=gitlab:$CI_COMMIT_REF_NAME:$CI_PROJECT_NAME \
        -Dsonar.projectName=gitlab:$CI_COMMIT_REF_NAME:$CI_PROJECT_NAME \
        -Dsonar.projectVersion=1.0.$CI_PIPELINE_ID \
        -Dsonar.sources=src \
        -Dsonar.sourceEncoding=UTF-8 \
        -Dsonar.issuesReport.html.enable=true \
        -Dsonar.gitlab.project_id=$CI_PROJECT_ID \
        -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA \
        -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
  tags:
    - centos-aliyun-158
  only:
    - master

build:
  stage: build
  script:
    - pwd
    - cnpm i
    - npm run lint
    - npm run build
    - rm -rf /home/<USER>/docker-data/nginx/html/xczx2-portal-java-dev.itheima.net
    - cp -rf ./dist /home/<USER>/docker-data/nginx/html/xczx2-portal-java-dev.itheima.net
  tags:
    - centos-aliyun-158
  only:
    - master
